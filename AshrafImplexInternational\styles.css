/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: #2c1810;
  background-color: #faf8f5;
  overflow-x: hidden;
}

body.loaded {
  animation: fadeIn 0.8s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-padding {
  padding: 100px 0;
}

/* Typography */
h1,
h2,
h3,
h4 {
  font-family: "Playfair Display", serif;
  font-weight: 600;
  line-height: 1.2;
}

.section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 20px;
  color: #8b4513;
  position: relative;
  opacity: 0;
  transform: translateY(30px);
  animation: slideInUp 0.8s ease forwards;
}

.section-subtitle {
  text-align: center;
  font-size: 1.1rem;
  color: #6b4423;
  max-width: 600px;
  margin: 0 auto 50px;
  opacity: 0;
  transform: translateY(20px);
  animation: slideInUp 0.8s ease 0.2s forwards;
}

.section-header {
  margin-bottom: 60px;
}

.section-divider {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #8b4513, #cd853f, #d2b48c);
  margin: 0 auto 30px;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.section-divider::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;
  height: 80px;
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.navbar .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 2rem;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
}

.navbar-logo {
  height: 50px;
  width: auto;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.brand-text {
  color: #8b4513;
  font-size: 1.6rem;
  font-weight: 600;
  font-family: "Playfair Display", serif;
  letter-spacing: 0.5px;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 0;
}

.navbar-item {
  color: #2c1810;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  padding: 12px 24px;
  position: relative;
  transition: all 0.3s ease;
  border-radius: 6px;
  letter-spacing: 0.3px;
}

.navbar-item:hover {
  color: #8b4513;
  background: rgba(139, 69, 19, 0.1);
}

.navbar-item.active {
  color: #8b4513;
  background: rgba(139, 69, 19, 0.15);
}

.navbar-item::after {
  content: "";
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: #8b4513;
  transition: width 0.3s ease;
}

.navbar-item:hover::after,
.navbar-item.active::after {
  width: 60%;
}

.navbar-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.inquire-btn {
  background: transparent;
  color: #8b4513;
  text-decoration: none;
  padding: 10px 18px;
  border: 2px solid #8b4513;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.inquire-btn:hover {
  background: #8b4513;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
}

.call-now-btn {
  background: #8b4513;
  color: white;
  text-decoration: none;
  padding: 12px 20px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(139, 69, 19, 0.3);
}

.call-now-btn:hover {
  background: #a0522d;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.4);
}

.navbar-burger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.navbar-burger:hover {
  background: rgba(139, 69, 19, 0.15);
}

.navbar-burger span {
  width: 25px;
  height: 3px;
  background: #2c1810;
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #2c1810 0%, #3d2317 100%);
  padding: 120px 0 50px;
  position: relative;
  overflow: hidden;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  max-width: 1200px;
  width: 100%;
}

.hero-text {
  padding-right: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.hero-title {
  font-size: 3.2rem;
  color: #f5deb3;
  margin-bottom: 25px;
  font-weight: 700;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 1.3rem;
  color: #e6d7c3;
  margin-bottom: 25px;
  font-weight: 500;
  line-height: 1.4;
}

.hero-description {
  font-size: 1.1rem;
  color: #d2b48c;
  margin-bottom: 35px;
  line-height: 1.6;
  max-width: 90%;
}

.cta-button {
  display: inline-block;
  padding: 18px 45px;
  background: #f5deb3;
  color: #8b4513;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.4s ease;
  border: 2px solid #f5deb3;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(245, 222, 179, 0.3);
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(245, 222, 179, 0.4);
  background: transparent;
  color: #f5deb3;
  border-color: #f5deb3;
}

.trust-indicators {
  display: flex;
  gap: 15px;
  margin-top: 30px;
  flex-wrap: wrap;
}

.trust-badge {
  padding: 8px 16px;
  background: rgba(245, 222, 179, 0.15);
  border: 1px solid rgba(245, 222, 179, 0.3);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.badge-text {
  color: #f5deb3;
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
}

.trust-badge:hover {
  background: rgba(245, 222, 179, 0.25);
  border-color: rgba(245, 222, 179, 0.5);
  transform: translateY(-2px);
}

.trust-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  background: white;
  border-radius: 15px;
  border: 2px solid #f5f2ed;
  transition: all 0.3s ease;
}

.trust-item:hover {
  border-color: #cd853f;
  transform: translateX(10px);
  box-shadow: 0 5px 15px rgba(139, 69, 19, 0.1);
}

.trust-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(139, 69, 19, 0.1);
  border-radius: 12px;
  color: #8b4513;
  flex-shrink: 0;
}

.trust-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.trust-title {
  font-weight: 600;
  font-size: 1rem;
  color: #8b4513;
}

.trust-subtitle {
  font-size: 0.85rem;
  color: #6b4423;
  font-weight: 400;
}

.hero-images {
  position: relative;
}

.hero-image-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
  height: 500px;
}

.hero-image-item {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(139, 69, 19, 0.2);
  transition: all 0.4s ease;
}

.hero-image-item.large {
  grid-row: 1 / 3;
}

.hero-image-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(139, 69, 19, 0.3);
}

.hero-image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.hero-image-item:hover img {
  transform: scale(1.1);
}

/* Trust Indicators */
.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
  z-index: 6;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  background: rgba(139, 69, 19, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(245, 222, 179, 0.3);
  transition: all 0.3s ease;
}

.scroll-indicator:hover {
  background: rgba(139, 69, 19, 0.3);
  transform: translateX(-50%) scale(1.1);
}

.scroll-arrow {
  width: 30px;
  height: 30px;
  border: 2px solid #f5deb3;
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
}

/* Animations */
@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

.fade-in {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease forwards;
}

.fade-in-delay {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease 0.3s forwards;
}

.fade-in-delay-2 {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease 0.6s forwards;
}

.fade-in-delay-3 {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease 0.9s forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Stats Section */
.stats {
  background: linear-gradient(rgba(139, 69, 19, 0.8), rgba(160, 82, 45, 0.8)), url('./images/leather2_4d00a569-932b-476f-a4b3-b0141b39e139.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  color: white;
  padding: 80px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  text-align: center;
}

.stat-item {
  padding: 20px;
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  color: #f5deb3;
  margin-bottom: 10px;
  font-family: "Playfair Display", serif;
}

.stat-label {
  font-size: 1.1rem;
  color: #e6d7c3;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Call to Action Section */
.cta-section {
  background: linear-gradient(135deg, #8b4513, #a0522d);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: #f5deb3;
  font-family: "Playfair Display", serif;
}

.cta-description {
  font-size: 1.2rem;
  margin-bottom: 40px;
  color: #e6d7c3;
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-btn {
  padding: 15px 30px;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.cta-btn.primary {
  background: #f5deb3;
  color: #8b4513;
  border: 2px solid #f5deb3;
}

.cta-btn.primary:hover {
  background: transparent;
  color: #f5deb3;
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.cta-btn.secondary {
  background: transparent;
  color: #f5deb3;
  border: 2px solid #f5deb3;
}

.cta-btn.secondary:hover {
  background: #f5deb3;
  color: #8b4513;
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* About Section */
.about {
  background: white;
}

.about-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.about-intro {
  font-size: 1.3rem;
  color: #8b4513;
  margin-bottom: 50px;
  font-weight: 500;
  line-height: 1.8;
}

.about-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  text-align: left;
}

.about-item {
  padding: 30px;
  background: #faf8f5;
  border-radius: 15px;
  border: 2px solid #f5f2ed;
  transition: all 0.3s ease;
}

.about-item:hover {
  border-color: #cd853f;
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(139, 69, 19, 0.1);
}

.about-item h3 {
  color: #8b4513;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.about-item p {
  color: #6b4423;
  line-height: 1.7;
}

/* Leadership Section */
.leadership {
  background: linear-gradient(135deg, #f5f2ed 0%, #ede7db 100%);
}

.leadership .team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

.leadership .team-member {
  text-align: center;
  padding: 40px 30px;
  background: white;
  border-radius: 20px;
  transition: all 0.4s ease;
  box-shadow: 0 10px 30px rgba(139, 69, 19, 0.1);
  border: 2px solid #f5f2ed;
  position: relative;
  overflow: hidden;
}

.leadership .team-member::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #8b4513, #cd853f);
  transition: left 0.4s ease;
}

.leadership .team-member:hover::before {
  left: 0;
}

.leadership .team-member:hover {
  transform: translateY(-15px);
  box-shadow: 0 25px 50px rgba(139, 69, 19, 0.2);
  border-color: #cd853f;
}

.team-avatar {
  width: 100px;
  height: 100px;
  margin: 0 auto 25px;
}

.avatar-placeholder {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, #8b4513, #a0522d);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  font-weight: 700;
  font-family: "Playfair Display", serif;
  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
  transition: all 0.3s ease;
}

.leadership .team-member:hover .avatar-placeholder {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(139, 69, 19, 0.4);
}

.avatar-initials {
  font-size: 2rem;
  font-weight: 700;
}

.leadership .team-member h4 {
  color: #8b4513;
  margin-bottom: 8px;
  font-size: 1.3rem;
}

.leadership .team-member p {
  color: #a0522d;
  font-size: 1rem;
  margin-bottom: 20px;
  font-weight: 500;
}

/* Services Section */
.services {
  background: linear-gradient(135deg, #f5f2ed 0%, #ede7db 100%);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.service-card {
  background: white;
  padding: 0;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 15px 35px rgba(139, 69, 19, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.service-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 20px 20px 0 0;
  position: relative;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.service-card:hover .service-image img {
  transform: scale(1.1);
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #8b4513, #cd853f, #daa520);
  transition: left 0.4s ease;
  z-index: 2;
}

.service-card:hover::before {
  left: 0;
}

.service-card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: 0 25px 50px rgba(139, 69, 19, 0.2);
}

.service-card h3 {
  color: #8b4513;
  margin: 25px 0 20px;
  font-size: 1.4rem;
  padding: 0 30px;
}

.service-card p {
  color: #6b4423;
  line-height: 1.7;
  padding: 0 30px 30px;
}

/* Products Section */
.products {
  background: #faf8f5;
}

.products-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: center;
}

.product-card {
  background: white;
  padding: 0;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 12px 30px rgba(139, 69, 19, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 2px solid #f0ebe0;
  overflow: hidden;
  position: relative;
  width: 100%;
  max-width: 320px;
}

/* Remove the centering styles for 4th and 5th products */
.products-grid .product-card:nth-child(4),
.products-grid .product-card:nth-child(5) {
  margin: 0;
}

@media (min-width: 769px) {
  .products-grid .product-card:nth-child(4),
  .products-grid .product-card:nth-child(5) {
    margin: 0;
  }
}

.product-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
  position: relative;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
  filter: sepia(20%) saturate(1.2);
}

.product-card:hover .product-image img {
  transform: scale(1.15);
}

.product-card:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow: 0 20px 45px rgba(139, 69, 19, 0.2);
  border-color: #cd853f;
}

.product-card h3 {
  color: #8b4513;
  margin: 20px 0 15px;
  font-size: 1.3rem;
  padding: 0 20px;
}

.product-card p {
  color: #6b4423;
  margin-bottom: 20px;
  line-height: 1.6;
  padding: 0 20px;
}

.product-uses {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  padding: 0 20px 25px;
}

.product-uses span {
  background: linear-gradient(135deg, #f5f2ed, #ede7db);
  color: #8b4513;
  padding: 6px 14px;
  border-radius: 25px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid #e6d7c3;
  transition: all 0.3s ease;
}

.product-uses span:hover {
  background: linear-gradient(135deg, #cd853f, #daa520);
  color: white;
  transform: translateY(-2px);
}

/* Product Features Section */
.product-features {
  margin-top: 60px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  padding: 40px;
  background: linear-gradient(135deg, #f8f5f0, #ede7db);
  border-radius: 15px;
  border: 1px solid #e6d7c3;
}

.feature-item {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.1);
  border: 1px solid #f0e6d2;
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.15);
}

.feature-item h4 {
  color: #8b4513;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #cd853f;
}

.feature-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-item li {
  color: #6b4423;
  padding: 8px 0;
  padding-left: 20px;
  position: relative;
  line-height: 1.6;
}

.feature-item li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #cd853f;
  font-weight: bold;
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .product-features {
    grid-template-columns: 1fr;
    gap: 25px;
    padding: 25px;
    margin-top: 40px;
  }

  .feature-item {
    padding: 20px;
  }

  .feature-item h4 {
    font-size: 1.2rem;
  }
}

/* Manufacturing Excellence Section */
.manufacturing {
  background: linear-gradient(135deg, #2c1810 0%, #3d2817 50%, #4a3426 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.manufacturing::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="manufacturing-pattern" patternUnits="userSpaceOnUse" width="40" height="40"><rect width="40" height="40" fill="none"/><circle cx="10" cy="10" r="2" fill="rgba(139,69,19,0.1)"/><circle cx="30" cy="30" r="1.5" fill="rgba(139,69,19,0.08)"/><rect x="15" y="15" width="10" height="10" fill="rgba(139,69,19,0.05)" rx="2"/></pattern></defs><rect width="100" height="100" fill="url(%23manufacturing-pattern)"/></svg>');
  opacity: 0.3;
}

.manufacturing .container {
  position: relative;
  z-index: 2;
}

.manufacturing .section-title {
  color: #f5deb3;
}

.manufacturing .section-subtitle {
  color: #e6d7c3;
}

.manufacturing-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-bottom: 80px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.manufacturing-item:nth-child(4) {
  grid-column: 2;
  justify-self: center;
}

.manufacturing-item {
  height: 300px;
}

.manufacturing-image {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  border-radius: 15px;
}

.manufacturing-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.manufacturing-item {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.4s ease;
}

.manufacturing-image {
  width: 100%;
  height: 250px;
  position: relative;
  overflow: hidden;
}

.manufacturing-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
  filter: sepia(30%) saturate(1.1) brightness(0.9);
}

.manufacturing-item:hover .manufacturing-image img {
  transform: scale(1.1);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(139, 69, 19, 0.9));
  padding: 30px 20px 20px;
  transform: translateY(100%);
  transition: transform 0.4s ease;
}

.manufacturing-item:hover .image-overlay {
  transform: translateY(0);
}

.image-overlay h4 {
  color: #f5deb3;
  font-size: 1.2rem;
  margin: 0;
}

.manufacturing-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.stat-item {
  text-align: center;
  padding: 40px 30px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 20px;
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #f5deb3, #daa520);
  transition: left 0.4s ease;
}

.stat-item:hover::before {
  left: 0;
}

.stat-item:hover {
  transform: translateY(-15px);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.stat-number {
  font-size: 3.5rem;
  font-weight: 700;
  color: #f5deb3;
  margin-bottom: 15px;
  font-family: "Playfair Display", serif;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  color: #e6d7c3;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Clients Section */
.clients {
  background: white;
}

.client-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

/* Suppliers Section */
.suppliers {
  background: linear-gradient(135deg, #8b4513, #a0522d);
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}

.suppliers::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('./images/leather2_4d00a569-932b-476f-a4b3-b0141b39e139.jpg');
  background-size: cover;
  background-position: center;
  opacity: 0.1;
  z-index: 1;
}

.suppliers .container {
  position: relative;
  z-index: 2;
}

.suppliers .section-title {
  color: #f5deb3;
  text-align: center;
  margin-bottom: 20px;
}

.suppliers .section-subtitle {
  color: #e6d7c3;
  text-align: center;
  margin-bottom: 50px;
}

.suppliers-carousel {
  position: relative;
  overflow: hidden;
  width: 100%;
}

.suppliers-grid {
  display: flex;
  gap: 40px;
  animation: scrollSuppliers 20s linear infinite;
  width: calc(300px * 8); /* Double the items for seamless loop */
}

.supplier-item {
  text-align: center;
  padding: 40px 30px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  transition: all 0.3s ease;
  border: 2px solid rgba(245, 222, 179, 0.3);
  flex: 0 0 260px;
  min-height: 250px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.supplier-item:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: #daa520;
}

.supplier-logo {
  width: 150px;
  height: 100px;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 15px;
  padding: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.supplier-logo img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.supplier-item h4 {
  color: #8b4513;
  font-size: 1.1rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

@keyframes scrollSuppliers {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.suppliers-grid:hover {
  animation-play-state: paused;
}

/* Markets Section */
.markets {
  background: linear-gradient(135deg, #f5f2ed 0%, #ede7db 100%);
}

.markets-grid {
  display: flex;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  justify-content: center;
}

.market-item {
  text-align: center;
  background: white;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(139, 69, 19, 0.1);
  border: 2px solid #f0ede8;
  flex: 1;
  min-width: 200px;
  max-width: 250px;
}

.market-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(139, 69, 19, 0.2);
  border-color: #cd853f;
}

.market-image {
  width: 100%;
  height: 150px;
  overflow: hidden;
}

.market-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.market-item:hover .market-image img {
  transform: scale(1.1);
}

.market-item h4 {
  color: #8b4513;
  font-size: 1.2rem;
  font-weight: 700;
  margin: 15px 0 10px;
}

.market-item p {
  color: #6b4423;
  font-size: 0.9rem;
  margin: 0 0 20px;
  padding: 0 15px;
}

.client-item,
.market-item {
  background: white;
  padding: 0;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 15px 35px rgba(139, 69, 19, 0.1);
  transition: all 0.4s ease;
  overflow: hidden;
  border: 2px solid #f5f2ed;
  position: relative;
}

.client-item::before,
.market-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #8b4513, #cd853f);
  transition: left 0.4s ease;
  z-index: 2;
}

.client-item:hover::before,
.market-item:hover::before {
  left: 0;
}

.client-item:hover,
.market-item:hover {
  transform: translateY(-15px);
  box-shadow: 0 25px 50px rgba(139, 69, 19, 0.2);
  border-color: #cd853f;
}

.client-image,
.market-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 20px 20px 0 0;
}

.client-image img,
.market-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
  filter: sepia(15%) saturate(1.1);
}

.client-item:hover .client-image img,
.market-item:hover .market-image img {
  transform: scale(1.1);
}

.client-item h4,
.market-item h4 {
  color: #8b4513;
  margin: 25px 0 15px;
  padding: 0 25px;
  font-size: 1.3rem;
}

.client-item p,
.market-item p {
  color: #6b4423;
  font-size: 1rem;
  padding: 0 25px 30px;
  line-height: 1.6;
}

/* Sustainability Section */
.sustainability {
  background: linear-gradient(135deg, #8b4513 0%, #a0522d 50%, #cd853f 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.sustainability::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80");
  background-size: cover;
  background-position: center;
  opacity: 0.1;
  z-index: 1;
}

.sustainability .container {
  position: relative;
  z-index: 2;
}

.sustainability .section-title {
  color: #f5deb3;
}

.sustainability .section-subtitle {
  color: #e6d7c3;
  font-size: 1.1rem;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

.sustainability-content {
  display: flex;
  gap: 60px;
  align-items: flex-start;
  margin-top: 60px;
}

.lwg-logo-section {
  flex: 0 0 300px;
  text-align: center;
}

.lwg-logo {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(245, 222, 179, 0.3);
}

.lwg-logo img {
  max-width: 100%;
  height: auto;
  max-height: 200px;
  object-fit: contain;
}

.sustainability-practices {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.practice-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 30px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(245, 222, 179, 0.2);
}

.practice-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(245, 222, 179, 0.4);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.practice-item h3 {
  color: #f5deb3;
  margin-bottom: 15px;
  font-size: 1.3rem;
  font-weight: 600;
}

.practice-item p {
  color: #e6d7c3;
  line-height: 1.6;
  margin: 0;
}



/* Contact Section */
.contact {
  background: #faf8f5;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.contact-info {
  display: grid;
  gap: 30px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 25px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f0ede8;
  position: relative;
  overflow: hidden;
}

.contact-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #8b4513, #cd853f);
  transition: left 0.4s ease;
}

.contact-item:hover::before {
  left: 0;
}

.contact-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.15);
  border-color: #cd853f;
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #8b4513, #a0522d);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.2);
  transition: all 0.3s ease;
}

.contact-item:hover .contact-icon {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(139, 69, 19, 0.3);
}

.contact-details h3 {
  color: #8b4513;
  margin-bottom: 8px;
  font-size: 1.2rem;
}

.contact-details p {
  color: #6b4423;
  font-size: 1rem;
  font-weight: 500;
}

.contact-cta {
  text-align: center;
  padding: 40px;
  background: linear-gradient(135deg, #8b4513, #a0522d);
  border-radius: 20px;
  color: white;
}

.contact-cta h3 {
  color: #f5deb3;
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.contact-cta p {
  color: #e6d7c3;
  margin-bottom: 30px;
  line-height: 1.7;
}

.contact-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 30px;
  flex-wrap: wrap;
}

.contact-buttons .cta-button {
  min-width: 150px;
}

/* Footer */
.footer {
  background: #2c1810;
  color: #d2b48c;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.footer-logo-image {
  height: 50px;
  width: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.footer-section h3,
.footer-section h4 {
  color: #f5deb3;
  margin-bottom: 20px;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section ul li a {
  color: #d2b48c;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #daa520;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #4a3426;
  color: #a0522d;
}

/* Enhanced Scroll Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.animate-on-scroll.animated {
  opacity: 1;
  transform: translateY(0);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #f5f2ed;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #8b4513, #cd853f);
  border-radius: 6px;
  border: 2px solid #f5f2ed;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #a0522d, #daa520);
}

/* Selection styling */
::selection {
  background: rgba(139, 69, 19, 0.3);
  color: #2c1810;
}

/* Focus styles for accessibility */
.nav-link:focus,
.cta-button:focus {
  outline: 2px solid #daa520;
  outline-offset: 2px;
}

/* Loading animation */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #8b4513, #cd853f, #daa520);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Floating animation */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.floating {
  animation: float 3s ease-in-out infinite;
}

/* Leather texture overlay */
.leather-texture::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><filter id="noise"><feTurbulence baseFrequency="0.9" numOctaves="4" stitchTiles="stitch"/><feColorMatrix values="0 0 0 0 0.545 0 0 0 0 0.271 0 0 0 0 0.075 0 0 0 0.1 0"/></filter></defs><rect width="100" height="100" filter="url(%23noise)" opacity="0.3"/></svg>');
  pointer-events: none;
  mix-blend-mode: multiply;
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar-burger {
    display: flex;
  }

  .navbar-menu {
    position: fixed;
    left: -100%;
    top: 80px;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.98);
    width: 100%;
    text-align: center;
    transition: 0.3s;
    padding: 20px 0;
    gap: 0;
    backdrop-filter: blur(15px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }

  .navbar-menu.active {
    left: 0;
  }

  .navbar-item {
    display: block;
    padding: 15px 20px;
    margin: 0;
    border-radius: 0;
    width: 100%;
  }

  .navbar-item::after {
    bottom: 5px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .clients-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .services-grid,
  .products-grid {
    grid-template-columns: 1fr;
  }

  .sustainability-content {
    flex-direction: column;
    gap: 40px;
  }

  .lwg-logo-section {
    flex: none;
    order: -1;
  }

  .sustainability-practices {
    grid-template-columns: 1fr;
  }

  .team-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .section-padding {
    padding: 60px 0;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-description {
    font-size: 0.9rem;
  }

  .container {
    padding: 0 15px;
  }
}

/* Clients Section */
.clients {
  background: white;
}

.client-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

/* Markets Section */
.markets {
  background: linear-gradient(135deg, #f5f2ed 0%, #ede7db 100%);
}

.markets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-text {
    padding-right: 0;
    order: 2;
  }

  .hero-images {
    order: 1;
  }

  .hero-image-grid {
    height: 300px;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr;
  }

  .hero-image-item.large {
    grid-row: 1;
    grid-column: 1 / 3;
  }

  .hero-image-item:nth-child(2),
  .hero-image-item:nth-child(3) {
    display: none;
  }

  .trust-indicators {
    flex-direction: column;
    align-items: center;
  }

  .markets-grid {
    flex-direction: column;
    gap: 15px;
    align-items: center;
  }

  .market-item {
    max-width: 300px;
    width: 100%;
  }

  .suppliers-grid {
    animation: none;
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .supplier-item {
    flex: none;
    width: 90%;
    max-width: 300px;
  }

  .navbar-actions {
    flex-direction: column;
    gap: 10px;
  }

  .inquire-btn,
  .call-now-btn {
    padding: 8px 16px;
    font-size: 0.8rem;
  }

  .contact-buttons {
    flex-direction: column;
    align-items: center;
  }

  .contact-buttons .cta-button {
    width: 100%;
    max-width: 250px;
  }

  .manufacturing-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .manufacturing-item:nth-child(4) {
    grid-column: 1;
    justify-self: stretch;
  }

  .products-grid .product-card:nth-child(4),
  .products-grid .product-card:nth-child(5) {
    margin: 0;
  }
}
